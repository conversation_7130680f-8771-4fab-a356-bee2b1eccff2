/**
 * 用户角色工具函数
 *
 * 提供检查用户角色和权限的工具函数
 */

import { Team } from "@/service/team-service"
import { useAtomValue } from 'jotai'
import { isTeamAdminAtom, currentTeamAtom } from '@/store/team-store'

/**
 * 检查用户在当前团队中是否为管理员
 *
 * 使用Jotai状态管理，从全局状态中获取管理员状态
 *
 * @returns 如果用户是管理员则返回true，否则返回false
 */
export const isTeamAdmin = (): boolean => {
  // 在服务器端渲染时，默认返回false
  if (typeof window === 'undefined') return false

  try {
    // 从localStorage中获取用户信息（兼容旧代码）
    const userStr = localStorage.getItem('user')
    if (!userStr) return false

    const userData = JSON.parse(userStr)

    // 获取当前团队ID
    const teamId = localStorage.getItem('team_id')
    if (!teamId) return false

    // 查找当前团队
    const currentTeam = userData.teams?.find((team: Team) => team.id === teamId)
    if (!currentTeam) return false

    // 检查用户在当前团队中的角色
    return currentTeam.roles?.includes('TEAM_ADMIN') || false
  } catch (error) {
    console.error('检查用户角色时出错:', error)
    return false
  }
}

/**
 * React Hook版本的isTeamAdmin
 *
 * 使用Jotai状态管理，从全局状态中获取管理员状态
 *
 * @returns 如果用户是管理员则返回true，否则返回false
 */
export const useIsTeamAdmin = (): boolean => {
  return useAtomValue(isTeamAdminAtom)
}

/**
 * 获取当前用户在当前团队中的角色
 *
 * @returns 用户角色数组，如果未找到则返回空数组
 */
export const getCurrentTeamRoles = (): string[] => {
  if (typeof window === 'undefined') return []

  try {
    // 获取当前团队ID
    const teamId = localStorage.getItem('team_id')
    if (!teamId) return []

    // 获取用户信息
    const userStr = localStorage.getItem('user')
    if (!userStr) return []

    const userData = JSON.parse(userStr)

    // 检查用户是否有teams属性
    if (!userData || !userData.teams || !Array.isArray(userData.teams)) {
      return []
    }

    // 查找当前团队
    const currentTeam = userData.teams.find((team: Team) => team.id === teamId)
    if (!currentTeam) return []

    // 返回用户在当前团队中的角色
    return currentTeam.roles || []
  } catch (error) {
    console.error('获取用户角色时出错:', error)
    return []
  }
}

/**
 * 获取当前用户信息
 *
 * @returns 用户信息对象，如果未找到则返回null
 */
export const getCurrentUser = () => {
  if (typeof window === 'undefined') return null

  try {
    const userStr = localStorage.getItem('user')
    if (!userStr) return null

    return JSON.parse(userStr)
  } catch (error) {
    console.error('获取用户信息时出错:', error)
    return null
  }
}

/**
 * 获取当前团队信息
 *
 * @returns 当前团队信息，如果未找到则返回null
 */
export const getCurrentTeam = () => {
  if (typeof window === 'undefined') return null

  try {
    // 获取当前团队ID
    const teamId = localStorage.getItem('team_id')
    if (!teamId) return null

    // 获取用户信息
    const userStr = localStorage.getItem('user')
    if (!userStr) return null

    const userData = JSON.parse(userStr)

    // 检查用户是否有teams属性
    if (!userData || !userData.teams || !Array.isArray(userData.teams)) {
      return null
    }

    // 查找并返回当前团队
    return userData.teams.find((team: Team) => team.id === teamId) || null
  } catch (error) {
    console.error('获取当前团队信息时出错:', error)
    return null
  }
}

/**
 * React Hook版本的getCurrentTeam
 *
 * 使用Jotai状态管理，从全局状态中获取当前团队信息
 *
 * @returns 当前团队信息，如果未找到则返回null
 */
export const useCurrentTeam = () => {
  return useAtomValue(currentTeamAtom)
}
