import { useSetModalState } from "@/hooks/common-hooks";
import { useConfigureModel, ConfiguredModel, Model } from "@/service";
import { useCallback, useState } from "react";
import { toast } from "sonner";

export interface IAddLlmRequestBody {
  llm_factory_id: string; // Ollama
  llm_name: string;
  model_type: string;
  api_base?: string; // chat|embedding|speech2text|image2text
  api_key: string;
}

export interface IApiKeySavingParams {
  llm_factory_id: string;
  api_key: string;
  llm_name: string;
  model_type: string;
  base_url?: string;
  api_base?: string;
  api_version?: string;
}

export const useSubmitApiKey = () => {
  const [savingParams, setSavingParams] = useState<Model>(
    {} as Model
  );
  const configMutate = useConfigureModel();
  const {
    visible: apiKeyVisible,
    hideModal: hideApiKeyModal,
    showModal: showApiKeyModal,
  } = useSetModalState();

  const onApiKeySavingOk = useCallback(
    (postBody: IApiKeySavingParams) => {
      configMutate.mutate({
        ...postBody,
      }, {
        onSuccess: () => {
          toast.success("模型配置成功");
          hideApiKeyModal();
        },
        onError: (error) => {
          console.error("配置模型失败:", error);
          toast.error("模型配置失败，请检查输入并重试");
        }
      });
    },
    [hideApiKeyModal, configMutate],
  );

  const onShowApiKeyModal = useCallback(
    (savingParams: Model) => {
      setSavingParams(savingParams);
      showApiKeyModal();
    },
    [showApiKeyModal, setSavingParams]
  );

  return {
    saveApiKeyLoading: configMutate.isPending,
    initialApiKey: "",
    llmFactory: savingParams,
    onApiKeySavingOk,
    apiKeyVisible,
    hideApiKeyModal,
    showApiKeyModal: onShowApiKeyModal,
  };
};

export const useSubmitNL2SQL = () => {
  //   const { addLlm, loading } = useAddLlm();
  const {
    visible: NL2SQLSettingVisible,
    hideModal: hideNL2SQLSettingModal,
    showModal: showNL2SQLSettingModal,
  } = useSetModalState();

  const onNL2SQLSettingOk = () => {};
  //   const onAzureAddingOk = useCallback(
  //     async (payload: IAddLlmRequestBody) => {
  //       const ret = await addLlm(payload);
  //       if (ret === 0) {
  //         hideAzureAddingModal();
  //       }
  //     },
  //     [hideAzureAddingModal, addLlm],
  //   );

  return {
    // AzureAddingLoading: loading,
    NL2SQLSettinLoading: false,
    onNL2SQLSettingOk,
    NL2SQLSettingVisible,
    hideNL2SQLSettingModal,
    showNL2SQLSettingModal,
  };
};
