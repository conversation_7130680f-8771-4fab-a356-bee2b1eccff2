"use client"


import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@ragtop-web/ui/components/dialog"
import { Button } from "@ragtop-web/ui/components/button"
import { Input } from "@ragtop-web/ui/components/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@ragtop-web/ui/components/form"
import { LLMFactory } from "@/common/model"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ragtop-web/ui/components/select"
import { Model, useConfigurabledModels } from "@/service"
import { IApiKeySavingParams } from "../hooks"

const llmFactoryToUrlMap = {
  [LLMFactory.Ollama]:
    'https://github.com/infiniflow/ragflow/blob/main/docs/guides/deploy_local_llm.mdx',
  [LLMFactory.Xinference]:
    'https://inference.readthedocs.io/en/latest/user_guide',
  // [LLMFactory.ModelScope]:
  //   'https://www.modelscope.cn/docs/model-service/API-Inference/intro',
  // [LLMFactory.LocalAI]: 'https://localai.io/docs/getting-started/models/',
  // [LLMFactory.LMStudio]: 'https://lmstudio.ai/docs/basics',
  // [LLMFactory.OpenAiAPICompatible]:
  //   'https://platform.openai.com/docs/models/gpt-4',
  // [LLMFactory.TogetherAI]: 'https://docs.together.ai/docs/deployment-options',
  // [LLMFactory.Replicate]: 'https://replicate.com/docs/topics/deployments',
  // [LLMFactory.OpenRouter]: 'https://openrouter.ai/docs',
  // [LLMFactory.HuggingFace]:
  //   'https://huggingface.co/docs/text-embeddings-inference/quick_tour',
  // [LLMFactory.GPUStack]: 'https://docs.gpustack.ai/latest/quickstart',
  [LLMFactory.VLLM]: 'https://docs.vllm.ai/en/latest/',
};

// 创建动态表单验证模式的函数
const createFormSchema = (requiresApiVersion: boolean) => z.object({
  api_key: z.string().min(1, "API Key 不能为空"),
  base_url: z.string().min(1, "基础URL不能为空"),
  model_type: z.string().min(1, "模型类型不能为空"),
  llm_name: z.string().min(1, "模型名称不能为空"),
  api_version: requiresApiVersion
    ? z.string().min(1, "API版本不能为空")
    : z.string().optional(),
})

type FormValues = z.infer<ReturnType<typeof createFormSchema>>

interface ApiKeyModalProps {
  open: boolean
  onClose: () => void
  onSubmit: (values: IApiKeySavingParams) => void
  loading?: boolean
  initialApiKey?: string
  llmFactory: Model
  title?: string
}

const modelsWithVersion = [LLMFactory.AzureOpenAI];

type LlmFactory = keyof typeof llmFactoryToUrlMap;



export function ApiKeyModal({
  open,
  onClose,
  onSubmit,
  loading = false,
  initialApiKey = "",
  llmFactory,
  title = "修改"
}: ApiKeyModalProps) {

  const { id, name, tags } = llmFactory
  const {data:options} = useConfigurabledModels(id)
  console.log(options)
  const url =
    llmFactoryToUrlMap[name as LlmFactory] ||
    'https://github.com/infiniflow/ragflow/blob/main/docs/guides/deploy_local_llm.mdx';

  const tagOptions = tags?.split(',').map(tag => ({
    value: tag,
    label: tag,
  })) || [];

  // 检查是否需要API版本字段
  const requiresApiVersion = modelsWithVersion.some((x) => x === name);

  // 创建对应的表单验证模式
  const formSchema = createFormSchema(requiresApiVersion);

  // 初始化表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      api_key: initialApiKey,
      base_url: "",
      llm_name: "",
      model_type: tagOptions[0]?.value,
      api_version: "",
    },
  })


  // 处理表单提交
  const handleSubmit = (values: FormValues) => {
    const finalValue: IApiKeySavingParams = {
      llm_factory_id: id,
      api_key: values.api_key,
      llm_name: values.llm_name,
      model_type: values.model_type,
      base_url: values.base_url,
      ...(values.api_version && { api_version: values.api_version })
    }
    onSubmit(finalValue)
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="model_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span className="text-red-500">*</span> 模型类型
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="选择模型类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {tagOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="llm_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span className="text-red-500">*</span> 模型名称:
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="选择模型名称" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {options?.map((option) => (
                        <SelectItem 
                        key={option.llm_name} 
                        value={option.llm_name}>
                          {option.llm_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="api_key"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span className="text-red-500">*</span> API-Key:
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="请输入api key (如果是本地部署的模型，请忽略它)" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />


            <FormField
              control={form.control}
              name="base_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    <span className="text-red-500">*</span> 基础url:
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="https://api.openai.com/v1" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {requiresApiVersion && (
              <FormField
                control={form.control}
                name="api_version"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      <span className="text-red-500">*</span> API版本:
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="https://api.openai.com/v1" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <DialogFooter className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
              >
                取消
              </Button>
              <Button
                type="submit"
                disabled={loading}
              >
                确定
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}