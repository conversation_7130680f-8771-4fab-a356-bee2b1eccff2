/**
 * 模型服务
 *
 * 提供模型配置相关的API请求方法
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createApiClient } from "@ragtop-web/ui/lib/api";
import { API_PREFIX } from "./index";

// 创建API客户端
const apiClient = createApiClient({
  apiPrefix: `${API_PREFIX}/llm`,
});

// 模型接口
export interface ConfiguredModel {
  llm_factory_id: string;
  llm_name: string;
  llm_id: string;
  model_type: string;
  api_key: string;
  api_base?: string;
  base_url?: string;
}

export interface Model {
  id: string;
  name: string;
  logo: string;
  tags: string;
}

export enum ModelType {
    Chat = "CHAT",
    Embedding = "EMBEDDING",
    Rerank = "RERANK",
}

export interface ConfiguredModelList {
   api_base: string;
    llm_factory: string;
    llm_id: string;
    llm_name: string;
    model_type?: ModelType;
}

export interface ConfigurabledModel {
    llm_factory: string;
    llm_id: string;
    llm_name: string;
    model_type?: ModelType;
}

/**
 * 获取已有的模型列表
 */
export const useExistingModels = () => {
  return useQuery({
    queryKey: ["existingModels"],
    queryFn: () => apiClient.post<ConfiguredModelList[]>(`/describe-models`),
  });
};

/**
 * 获取没有配置的模型列表
 */
export const useUnconfiguredModels = () => {
  return useQuery({
    queryKey: ["unconfiguredModels"],
    queryFn: () => apiClient.post<Model[]>(`/describe-model-factories`),
  });
};

/**
 * 删除某个模型
 */
export const useDeleteModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {llm_id: string}) => apiClient.post(`/delete-model`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["existingModels"] });
    },
  });
};

/**
 * 配置模型
 */
export const useConfigureModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      llm_factory_id: string;
      llm_name: string;
      model_type: string;
      api_key: string;
      api_base?: string;
      base_url?: string;
    }) => apiClient.post(`/configure-model`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["existingModels"] });
      queryClient.invalidateQueries({ queryKey: ["unconfiguredModels"] });
    },
  });
};

export const useConfigurabledModels = (id:string) => {
  return useQuery({
    queryKey: ["configurabledModels",id],
    queryFn: () => apiClient.post<ConfigurabledModel[]>(`/describe-configurable-models`,{llm_factory_id:id}),
    enabled:!!id
  });
};